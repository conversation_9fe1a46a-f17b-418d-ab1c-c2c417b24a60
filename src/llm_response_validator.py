#!/usr/bin/env python3
"""
🛡️ LLM RESPONSE VALIDATOR - AUTOMATIC CORRECTION SYSTEM

This module implements automatic validation and correction of LLM responses
to ensure they conform to the expected JSON schema format.

Phase 1: Basic validation with retry logic
Phase 2: Enhanced error categorization and specific feedback
"""

import json
import time
import logging
from typing import Tuple, List, Optional, Dict, Any
from backtesting_rule_parser import SchemaBasedPatternParser, BacktestingRuleParseError, TradingPattern
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMResponseValidator:
    """Validates and corrects LLM responses for JSON schema compliance"""
    
    def __init__(self, max_retries: int = 2, retry_delay: float = 1.0):
        """
        Initialize the validator
        
        Args:
            max_retries: Maximum number of correction attempts
            retry_delay: Delay between retry attempts (seconds)
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.parser = SchemaBasedPatternParser()
        
        # Statistics tracking
        self.stats = {
            'total_validations': 0,
            'successful_first_attempt': 0,
            'successful_after_correction': 0,
            'failed_validations': 0,
            'error_categories': {}
        }
    
    def validate_and_correct(self, llm_client, original_prompt: str, llm_response: str) -> Tuple[str, List[TradingPattern], bool]:
        """
        Validate LLM response and request correction if needed
        
        Args:
            llm_client: LLM client for sending correction requests
            original_prompt: The original Stage 2 prompt sent to LLM
            llm_response: The LLM's response to validate
            
        Returns:
            Tuple of (corrected_response, parsed_patterns, was_corrected)
        """
        self.stats['total_validations'] += 1
        
        logger.info("🔍 Validating LLM response for JSON schema compliance...")
        
        # Attempt 0: Try original response
        try:
            patterns = self.parser.parse_llm_response(llm_response)
            logger.info(f"✅ Original response valid - parsed {len(patterns)} patterns")
            self.stats['successful_first_attempt'] += 1
            return llm_response, patterns, False
            
        except Exception as e:
            logger.warning(f"⚠️ Original response failed validation: {str(e)}")
            error_category = self._categorize_error(e, llm_response)
            self._update_error_stats(error_category)
        
        # Retry attempts with correction
        current_response = llm_response
        
        for attempt in range(1, self.max_retries + 1):
            logger.info(f"🔄 Correction attempt {attempt}/{self.max_retries}")
            
            try:
                # Generate correction prompt
                correction_prompt = self._generate_correction_prompt(
                    original_prompt, current_response, error_category, attempt
                )
                
                # Add retry delay
                if attempt > 1:
                    time.sleep(self.retry_delay * attempt)
                
                # Request correction from LLM
                logger.info("📤 Sending correction request to LLM...")
                response_data = llm_client.send_message(
                    correction_prompt,
                    model=None,  # Let LMStudioClient choose the working model
                    temperature=config.LLM_TRANSLATION_TEMPERATURE,
                    max_tokens=config.LLM_MAX_TOKENS,
                    context_length=config.LLM_CONTEXT_LENGTH
                )

                # Extract the response text from the response data
                if isinstance(response_data, dict) and 'response' in response_data:
                    current_response = response_data['response']
                else:
                    current_response = str(response_data)
                
                # Validate corrected response
                patterns = self.parser.parse_llm_response(current_response)
                logger.info(f"✅ Correction successful - parsed {len(patterns)} patterns")
                self.stats['successful_after_correction'] += 1
                return current_response, patterns, True
                
            except Exception as e:
                logger.warning(f"⚠️ Correction attempt {attempt} failed: {str(e)}")
                error_category = self._categorize_error(e, current_response)
                
                if attempt == self.max_retries:
                    # Final attempt failed
                    logger.error(f"❌ All correction attempts failed. Final error: {str(e)}")
                    self.stats['failed_validations'] += 1
                    raise BacktestingRuleParseError(
                        f"LLM response validation failed after {self.max_retries} correction attempts. "
                        f"Final error: {str(e)}"
                    )
        
        # Should never reach here, but just in case
        self.stats['failed_validations'] += 1
        return current_response, [], True
    
    def _categorize_error(self, error: Exception, response: str) -> str:
        """Categorize the type of error for targeted correction"""
        error_str = str(error).lower()
        response_lower = response.lower()
        
        # JSON parsing errors
        if 'json' in error_str or 'expecting' in error_str:
            if not response.strip().startswith('{') and not response.strip().startswith('['):
                return 'not_json_format'
            elif 'expecting' in error_str:
                return 'malformed_json'
            else:
                return 'json_parse_error'
        
        # Schema validation errors
        elif 'missing' in error_str or 'required' in error_str:
            if 'pattern_name' in error_str:
                return 'missing_pattern_name'
            elif 'entry_conditions' in error_str:
                return 'missing_entry_conditions'
            elif 'exit_conditions' in error_str:
                return 'missing_exit_conditions'
            else:
                return 'missing_required_fields'
        
        # Condition validation errors
        elif 'condition' in error_str:
            return 'invalid_condition_type'
        
        # Empty or no patterns
        elif 'no fallback' in error_str or 'no patterns' in error_str:
            return 'no_patterns_found'
        
        # Default category
        else:
            return 'unknown_error'
    
    def _update_error_stats(self, error_category: str):
        """Update error statistics"""
        if error_category not in self.stats['error_categories']:
            self.stats['error_categories'][error_category] = 0
        self.stats['error_categories'][error_category] += 1
    
    def _generate_correction_prompt(self, original_prompt: str, failed_response: str, 
                                  error_category: str, attempt: int) -> str:
        """Generate a targeted correction prompt based on error category"""
        
        base_correction = f"""🚨 FORMAT ERROR DETECTED - CORRECTION REQUIRED

Your previous response could not be parsed. Here's what went wrong and how to fix it:

ERROR CATEGORY: {error_category.replace('_', ' ').title()}
ATTEMPT: {attempt}

FAILED RESPONSE:
{failed_response}

"""
        
        # Add specific guidance based on error category
        if error_category == 'not_json_format':
            guidance = """
🎯 SPECIFIC ISSUE: Your response is not in JSON format.

REQUIRED FIX: Provide ONLY valid JSON, starting with { or [
Do NOT include explanatory text before or after the JSON.

CORRECT FORMAT:
{
  "pattern_name": "Your Pattern Name",
  "entry_conditions": [...],
  "exit_conditions": [...]
}
"""
        
        elif error_category == 'malformed_json':
            guidance = """
🎯 SPECIFIC ISSUE: Your JSON has syntax errors (missing commas, brackets, quotes).

REQUIRED FIX: Ensure proper JSON syntax:
- All strings in double quotes
- Commas between array/object elements  
- Matching brackets and braces
- No trailing commas
"""
        
        elif error_category == 'missing_pattern_name':
            guidance = """
🎯 SPECIFIC ISSUE: Missing required "pattern_name" field.

REQUIRED FIX: Every pattern MUST have a "pattern_name" field:
{
  "pattern_name": "Descriptive Pattern Name",
  ...
}
"""
        
        elif error_category == 'missing_entry_conditions':
            guidance = """
🎯 SPECIFIC ISSUE: Missing required "entry_conditions" field.

REQUIRED FIX: Every pattern MUST have "entry_conditions" array:
{
  "pattern_name": "...",
  "entry_conditions": [
    {
      "condition": "close_above_high",
      "lookback": 1
    }
  ],
  ...
}
"""
        
        elif error_category == 'missing_exit_conditions':
            guidance = """
🎯 SPECIFIC ISSUE: Missing required "exit_conditions" field.

REQUIRED FIX: Every pattern MUST have "exit_conditions" array:
{
  "pattern_name": "...",
  "entry_conditions": [...],
  "exit_conditions": [
    {
      "condition": "risk_reward_ratio",
      "risk": 1,
      "reward": 2
    }
  ]
}
"""
        
        elif error_category == 'invalid_condition_type':
            guidance = """
🎯 SPECIFIC ISSUE: Using invalid condition types.

REQUIRED FIX: Use ONLY these approved condition types:
Entry: close_above_high, close_below_low, range_expansion, range_contraction, consecutive_days
Exit: risk_reward_ratio, fixed_stop_loss, fixed_take_profit, trailing_stop
"""
        
        elif error_category == 'no_patterns_found':
            guidance = """
🎯 SPECIFIC ISSUE: No valid patterns could be extracted.

REQUIRED FIX: Provide at least one complete pattern with all required fields:
- pattern_name (string)
- entry_conditions (array with at least one condition)
- exit_conditions (array with at least one condition)
"""
        
        else:
            guidance = """
🎯 GENERAL ISSUE: Response doesn't match expected JSON schema.

REQUIRED FIX: Follow the exact JSON schema format from the original prompt.
Ensure all required fields are present and properly formatted.
"""
        
        correction_prompt = base_correction + guidance + f"""

🎯 CORRECTED RESPONSE REQUIRED:
Provide ONLY the corrected JSON response. No explanations, no additional text.
Just the valid JSON that follows the schema requirements.

CORRECTED JSON:
"""
        
        return correction_prompt
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get validation statistics"""
        total = self.stats['total_validations']
        if total == 0:
            return self.stats
        
        return {
            **self.stats,
            'success_rate_first_attempt': (self.stats['successful_first_attempt'] / total) * 100,
            'success_rate_after_correction': (self.stats['successful_after_correction'] / total) * 100,
            'overall_success_rate': ((self.stats['successful_first_attempt'] + self.stats['successful_after_correction']) / total) * 100,
            'failure_rate': (self.stats['failed_validations'] / total) * 100
        }
    
    def reset_statistics(self):
        """Reset validation statistics"""
        self.stats = {
            'total_validations': 0,
            'successful_first_attempt': 0,
            'successful_after_correction': 0,
            'failed_validations': 0,
            'error_categories': {}
        }
