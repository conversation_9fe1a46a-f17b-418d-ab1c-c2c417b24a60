#!/usr/bin/env python3
"""
Debug script to test LLM response validation
"""

import sys
import os
sys.path.append('src')

from ai_integration.lm_studio_client import LMStudioClient
from ai_integration.pattern_translation_prompts import PatternTranslationPrompts
from backtesting_rule_parser import SchemaBasedPatternParser
from config import config

def test_llm_response():
    """Test what the LLM is actually outputting"""
    
    # Create LM Studio client
    client = LMStudioClient(config.LM_STUDIO_URL)
    
    # Check if server is running
    if not client.is_server_running():
        print("❌ LM Studio server is not running")
        return
    
    print("✅ LM Studio server is running")
    
    # Create a simple test prompt for Stage 2 translation
    test_patterns = """
    PATTERN 1: London Opening Range Breakout
    During the London session opening (9:00 AM UTC+1), when price breaks above the first 30-minute range high, 
    institutional flows typically drive price higher as retail participants chase the breakout.
    
    PATTERN 2: New York Session Range Breakdown  
    When NY session opens and price breaks below the first 60-minute range low, institutional selling pressure 
    typically accelerates as stop losses are triggered.
    """
    
    # Generate Stage 2 prompt
    prompt = PatternTranslationPrompts.generate_stage2_translation_prompt(test_patterns, "DEUIDXEUR")
    
    print(f"📏 Prompt size: {len(prompt)} characters")
    print("🔄 Sending to LLM...")
    
    try:
        # Send to LLM
        response = client.send_message(
            prompt,
            model=None,
            temperature=config.LLM_TRANSLATION_TEMPERATURE,
            max_tokens=config.LLM_MAX_TOKENS,
            context_length=config.LLM_CONTEXT_LENGTH
        )
        
        # Extract response text
        if isinstance(response, dict) and 'response' in response:
            llm_response = response['response']
        else:
            llm_response = str(response)
        
        print(f"📏 Response size: {len(llm_response)} characters")
        print("🔍 LLM Response:")
        print("=" * 80)
        print(llm_response)
        print("=" * 80)
        
        # Try to validate the response
        print("\n🛡️ Testing validation...")
        parser = SchemaBasedPatternParser()
        
        try:
            patterns = parser.parse_llm_response(llm_response)
            print(f"✅ Validation successful! Parsed {len(patterns)} patterns")
            for i, pattern in enumerate(patterns):
                print(f"   Pattern {i+1}: {pattern.pattern_name}")
        except Exception as e:
            print(f"❌ Validation failed: {str(e)}")
            print(f"   Error type: {type(e).__name__}")
            
            # Try to extract JSON manually to see what's wrong
            import json
            import re
            
            print("\n🔍 Attempting manual JSON extraction...")
            
            # Look for JSON-like structures
            json_matches = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', llm_response, re.DOTALL)
            
            if json_matches:
                print(f"Found {len(json_matches)} potential JSON objects:")
                for i, match in enumerate(json_matches):
                    print(f"\nJSON {i+1}:")
                    print("-" * 40)
                    print(match[:500] + "..." if len(match) > 500 else match)
                    
                    try:
                        parsed = json.loads(match)
                        print("✅ Valid JSON structure")
                        print(f"   Keys: {list(parsed.keys())}")
                    except json.JSONDecodeError as je:
                        print(f"❌ Invalid JSON: {je}")
            else:
                print("❌ No JSON-like structures found")
                
                # Show first 1000 characters of response
                print("\nFirst 1000 characters of response:")
                print("-" * 40)
                print(llm_response[:1000])
        
    except Exception as e:
        print(f"❌ Error communicating with LLM: {e}")

if __name__ == "__main__":
    test_llm_response()
